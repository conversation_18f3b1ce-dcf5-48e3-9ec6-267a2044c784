version: '3.5'
# 网络配置
networks:
  backend:
    driver: ${NETWORKS_DRIVER}

volumes:
  feed-icons:
  postgres:

# 服务容器配置
services:
  #  golang:
  #    build:
  #      context: ./golang
  #    environment:
  #      - TZ=${TZ}
  #    volumes:
  #      - ${CODE_PATH_HOST}:/usr/src/code # 挂载代码
  #    ports:
  #      - "8000:8000"
  #      - "8001:8001"
  #      - "8002:8002"
  #      - "8003:8003"
  #      - "9000:9000"
  #      - "9001:9001"
  #      - "9002:9002"
  #      - "9003:9003"
  #    stdin_open: true
  #    tty: true
  #    networks:
  #      - backend
  #    restart: always

  #  etcd:
  #    image: bitnami/etcd
  #    environment:
  #      - TZ=${TZ}
  #      - ALLOW_NONE_AUTHENTICATION=yes
  #      - ETCD_ADVERTISE_CLIENT_URLS=http://etcd:2379
  #    ports:
  #      - "${ETCD_PORT}:2379"
  #    networks:
  #      - backend
  #    restart: always

  #  mysql:
  #    image: mysql:5.7
  #    environment:
  #      - TZ=${TZ}
  #      - MYSQL_USER=${MYSQL_USERNAME}
  #      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
  #      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
  #    volumes:
  #      - ${DATA_PATH_HOST}/mysql:/var/lib/mysql
  #    ports:
  #      - "${MYSQL_PORT}:3306"
  #    networks:
  #      - backend
  #    restart: always

  postgres:
    image: postgres:16-alpine
    container_name: ktf-pgsql
    environment:
      - POSTGRES_PASSWORD=ttrss # feel free to change the password
    volumes:
      - postgres:/var/lib/postgresql/data # persist postgres data to ~/postgres/data/ on the host
    networks:
      - backend
    ports:
      - "${PGSQL_PORT}:5432"
    restart: always

  redis:
    image: redis:6.2-alpine
    environment:
      - TZ=${TZ}
    volumes:
      - ${DATA_PATH_HOST}/redis:/data
    ports:
      - "${REDIS_PORT}:6379"
    networks:
      - backend
    restart: always


  #  prometheus:
  #    image: bitnami/prometheus
  #    environment:
  #      - TZ=${TZ}
  #    volumes:
  #      - ./prometheus/prometheus.yml:/opt/bitnami/prometheus/conf/prometheus.yml
  #    ports:
  #      - "${PROMETHEUS_PORT}:9090"
  #    networks:
  #      - backend
  #    restart: always

  #  grafana:
  #    image: grafana/grafana
  #    environment:
  #      - TZ=${TZ}
  #    ports:
  #      - "${GRAFANA_PORT}:3000"
  #    networks:
  #      - backend
  #    restart: always

  #  jaeger:
  #    image: jaegertracing/all-in-one:1.28
  #    environment:
  #      - TZ=${TZ}
  #    ports:
  #      - "${JAEGER_PORT}:16686"
  #    networks:
  #      - backend
  #    restart: always

  #  dtm:
  #    image: yedf/dtm
  #    environment:
  #      - TZ=${TZ}
  #    entrypoint:
  #      - "/app/dtm/dtm"
  #      - "-c=/app/dtm/configs/config.yaml"
  #    volumes:
  #      - ./dtm/config.yml:/app/dtm/configs/config.yaml
  #    ports:
  #      - "${DTM_HTTP_PORT}:36789"
  #      - "${DTM_GRPC_PORT}:36790"
  #    networks:
  #      - backend
  #    restart: always


  #  mysql-manage:
  #    image: phpmyadmin/phpmyadmin
  #    environment:
  #      - TZ=${TZ}
  #      - PMA_ARBITRARY=1
  #      - MYSQL_USER=${MYSQL_MANAGE_USERNAME}
  #      - MYSQL_PASSWORD=${MYSQL_MANAGE_PASSWORD}
  #      - MYSQL_ROOT_PASSWORD=${MYSQL_MANAGE_ROOT_PASSWORD}
  #      - PMA_HOST=${MYSQL_MANAGE_CONNECT_HOST}
  #      - PMA_PORT=${MYSQL_MANAGE_CONNECT_PORT}
  #    ports:
  #      - "${MYSQL_MANAGE_PORT}:80"
  #    depends_on:
  #      - mysql
  #    networks:
  #      - backend
  #    restart: always