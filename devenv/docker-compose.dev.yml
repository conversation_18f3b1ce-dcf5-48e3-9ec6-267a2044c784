---
version: "3.7"

services:
  postgres:
    image: dp.lucc.dev/postgres:17
    container_name: pgsql
    restart: unless-stopped
    environment:
      POSTGRES_USER: pgsql
      POSTGRES_PASSWORD: 159357
      POSTGRES_DB: popjob
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    expose:
      - "5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./pgsql/pg_hba.conf:/var/lib/foo/pg_hba.conf
    command: ["postgres", "-c", "hba_file=/var/lib/foo/pg_hba.conf"]


  redis:
    container_name: redis
    image: dp.lucc.dev/redis:7.4
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - popjob
    volumes:
      - redis_data:/var/lib/redis

networks:
  popjob:
    driver: "bridge"

volumes:
  postgres_data:
  redis_data:
  mongodb_data: