---
version: '3'


#- docker-compose logs -tf --tail 10
#- docker-compose up --detach --build <container-name> # 在docker-compose中重新启动单个容器
#- docker-compose down && docker-compose up --build -d
#- docker compose up -d --build # 启动服务，重新编译镜像，最有用的命令
#- docker compose build # 进行所需的服务镜像构建
#- docker compose config # 查看docker-compose配置文件
#- docker compose down # 停掉服务，删除容器，不删除镜像 docker stop container && docker rm container
#- docker compose events # 接受服务之间的互动事件，如进行健康检查等
#- docker compose exec # 进入容器，或者对某个容器执行命令
#- docker compose images # 列出所有镜像
#- docker compose top # 显示各个容器内运行的进程


#- url: https://github.com/lavie/runlike
#  des: 类似chrome 之于 copy as curl，用来一键生成非常复杂的docker run命令。快速获取 Docker 容器启动命令的工具。这是一个用于解析运行中容器的工具，可自动生成对应的 docker run 启动命令。它能够提取容器的配置信息，包括端口绑定、映射卷、环境变量、网络设置等，适用于复制、调试或迁移容器的场景。 # 具体情况如下，事情要从上周的一次事故说起，我们用 docker 部署的程序有一点问题，要马上回滚到上一个版本。这个 docker 是一个比较复杂的和 BPF 有关的程序，启动时候需要设置很多 mount 和 environments，docker run 的命令特别长。所以我用 Ansible 来配置好这些变量，然后启动 docker，一个实例要花费 3～5 分钟才能启动。同事突然说，某实例他手动启动了，当时我就震惊了，怎么手速这么快？！请教了一下，原来是用的 runlike 工具。这个工具的原理是，`docker inspect <container-name> | runlike --stdin` ，就会生成这个容器的 docker run 命令。


#- url: https://github.com/containers/skopeo
#  des: 一个命令行工具，用于在不同的容器镜像仓库之间复制、检查和删除容器镜像


tasks:
  dc-up:
    desc: 构建镜像并启动服务
    cmds:
      - docker compose up -d --build

  dc-single:
    desc: 重建单个服务
    cmds:
      - docker compose up -d --build {{.SERVICE}}

  dc-logs:
    desc: 查看服务日志（实时跟踪）
    cmds:
      - docker compose logs -tf --tail 10

  dc-exec:
    desc: 进入容器终端
    cmds:
      - docker compose exec {{.SERVICE}} /bin/bash

  dc-down:
    desc: 停止并删除容器
    cmds:
      - docker compose down

# ========================
# 环境变量示例（.env 文件）
# ========================
# APP_NAME=myapp
# IMAGE=nginx:latest
# PORT=8080
# CONTAINER=mycontainer
# VOLUME=app_data
# SERVICE=web



  # [etolbakov/excalidocker-rs: Convert your docker-compose into excalidraw](https://github.com/etolbakov/excalidocker-rs)
  dc2exd:
    status:
      - command -v excalidocker
    preconditions:
      - brew install etolbakov/taps/excalidocker
    cmd: excalidocker --input-path docker-compose.yml --output-path result.excalidraw

  # TODO [kubernetes/kompose：将 Compose 转换为 Kubernetes --- kubernetes/kompose: Convert Compose to Kubernetes](https://github.com/kubernetes/kompose)
  kompose:
    desc: 把 docker-compose 转换为 k8s
