version: "3"

vars:
  # 定义 fetch binary 下载相关的变量
  FETCH_REPO: gruntwork-io/fetch
  FETCH_BINARY_BASE_NAME: fetch_linux_amd64
  # 直接写死版本号，因为 fetch 稳定不常更新
  LATEST_FETCH_VERSION: v0.4.6
  # 构建下载 URL
  FETCH_DOWNLOAD_URL: "https://github.com/{{.FETCH_REPO}}/releases/download/{{.LATEST_FETCH_VERSION}}/{{.FETCH_BINARY_BASE_NAME}}"

tasks:
  default:
    cmds:
      - task: install-fetch
      - task: download
      - task: list
    desc: 默认任务，安装fetch，并通过fetch拉取指定文件夹到本地

  install-fetch:
    desc: Install gruntwork-io/fetch binary
    cmds:
      # 移除了 curl 和 grep 动态获取版本号的逻辑，直接使用硬编码的版本号
      - curl -LO "{{.FETCH_DOWNLOAD_URL}}"
      - chmod +x "{{.FETCH_BINARY_BASE_NAME}}"
      - sudo mv "{{.FETCH_BINARY_BASE_NAME}}" /usr/local/bin/fetch

  # TODO 需要做更多通用化修改，比如 repo、指定文件夹
  download:
    desc: Download docs-images using fetch
    cmd: fetch --repo="https://github.com/xbpk3t/docs-images" --branch="main" --github-oauth-token="${PAT}" web/public

  list:
    desc: List contents of web/public/algo
    cmd: ls -lR web/public || echo "web/public/algo not found or empty"
