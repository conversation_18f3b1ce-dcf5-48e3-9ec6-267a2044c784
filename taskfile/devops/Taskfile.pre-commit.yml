version: "3"

# autoupdate          Auto-update pre-commit config to the latest repos' versions.
# clean               Clean out pre-commit files.
# gc                  Clean unused cached repos.
# init-templatedir    Install hook script in a directory intended for use with `git config init.templateDir`.
# install             Install the pre-commit script.
# install-hooks       Install hook environments for all environments in the config file. You may find `pre-commit install --install-
# hooks` more useful.
# migrate-config      Migrate list configuration to new map configuration.
# run                 Run hooks.
# sample-config       Produce a sample .pre-commit-config.yaml file
# try-repo            Try the hooks in a repository, useful for developing new hooks.
# uninstall           Uninstall the pre-commit script.
# validate-config     Validate .pre-commit-config.yaml files
# validate-manifest   Validate .pre-commit-hooks.yaml files
# help                Show help for a specific command.

tasks:
  # Commented out to prevent auto-execution when included
  default:
    cmds:
      - pre-commit run --all-files  # 检查所有文件（非暂存区）
    dir: '{{.USER_WORKING_DIR}}'
    preconditions:
      - task: update-hooks

  # 新增维护任务
  maintain:
    cmds:
      - pre-commit clean  # 清理缓存
      - pre-commit gc     # 垃圾回收
      - pre-commit install-hooks  # 重装钩子
    dir: '{{.USER_WORKING_DIR}}'

  init-template:
    cmds:
      - pre-commit init-templatedir {{.CLI_ARGS}}  # 初始化Git模板
    dir: '{{.USER_WORKING_DIR}}'

  uninstall:
    cmds:
      - pre-commit uninstall  # 卸载钩子
    dir: '{{.USER_WORKING_DIR}}'

  migrate:
    cmds:
      - pre-commit migrate-config  # 配置文件迁移
    dir: '{{.USER_WORKING_DIR}}'

  try-repo:
    cmds:
      - pre-commit try-repo {{.CLI_ARGS}}  # 测试新仓库
    dir: '{{.USER_WORKING_DIR}}'

  # 原有任务优化
  #  hook:
  #    cmds:
  #      - pre-commit run {{.CLI_ARGS}}  # 支持指定钩子/文件
  #    dir: '{{.USER_WORKING_DIR}}'


  hook:
    desc: "交互式选择或直接运行检查"
    interactive: true
    silent: true
    dir: '{{.USER_WORKING_DIR}}'
    cmd: |
      if [ -n "{{.CLI_ARGS}}" ]; then
        pre-commit run {{.CLI_ARGS}}
      else
        SELECTED=$(gum choose "yamllint" "markdownlint" "gitleaks")
        if [[ -n "$SELECTED" ]]; then
          pre-commit run "$SELECTED"
        fi
      fi


  update-hooks: # 重命名更准确
    cmds:
      - pre-commit autoupdate  # 更新远程仓库版本
    dir: '{{.USER_WORKING_DIR}}'
