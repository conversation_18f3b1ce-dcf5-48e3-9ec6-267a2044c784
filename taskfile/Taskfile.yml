---
version: '3'

includes:
  # All includes working properly
  git: taskfile/devops/Taskfile.git.yml
  linters: taskfile/devops/Taskfile.linters.yml

  go: taskfile/go/Taskfile.go.yml
  k3d: taskfile/k8s/Taskfile.k3d.yml
  docker: taskfile/k8s/Taskfile.docker.yml
  ansible: taskfile/devops/Taskfile.ansible.yml
  gr: taskfile/devops/Taskfile.goreleaser.yml
  caddy: taskfile/devops/Taskfile.caddy.yml
  pc: taskfile/devops/Taskfile.pre-commit.yml
  wrangler: taskfile/devops/Taskfile.wrangler.yml
  rclone: taskfile/devops/Taskfile.rclone.yml
  tcpdump: taskfile/devops/Taskfile.tcpdump.yml
  tf: taskfile/devops/Taskfile.tf.yml
  #  gz: taskfile/go/Taskfile.gz.yml # TODO 这个有bug暂时注释掉
  mk: taskfile/k8s/Taskfile.minikube.yml
  dc: taskfile/k8s/Taskfile.docker-compose.yml
  helm: taskfile/k8s/Taskfile.helm.yml
  k8s: taskfile/k8s/Taskfile.k8s.yml

  mac: taskfile/mac/Taskfile.mac.yml
  mac-cleanup: taskfile/mac/Taskfile.mac-cleanup.yml
  brew: taskfile/mac/Taskfile.brew.yml
  dotbot: taskfile/mac/Taskfile.dotbot.yml
  nix: taskfile/mac/Taskfile.nix.yml

  img: taskfile/mac/Taskfile.img.yml
  trzsz: taskfile/mac/Taskfile.trzsz.yml
  net: taskfile/mac/Taskfile.network.yml
  scrapy: taskfile/mac/Taskfile.scrapy.yml
  me: taskfile/mac/Taskfile.me.yml
  alfred: taskfile/mac/Taskfile.alfred.yml
  pnpm: taskfile/mac/Taskfile.pnpm.yml
  goland: taskfile/mac/Taskfile.goland.yml


tasks:
  default:
    silent: true
    cmd: task -l
