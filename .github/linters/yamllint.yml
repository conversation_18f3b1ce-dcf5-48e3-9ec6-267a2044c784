---

# [Rules — yamllint 1.29.0 documentation](https://yamllint.readthedocs.io/en/stable/rules.html)
extends: default
yaml-files:
  - "*.yml"
  - "*.yaml"
  - "*.yamllint"
rules:
  document-start: disable
  line-length:
    max: 3000
    level: warning
  commas:
    max-spaces-before: 0
    min-spaces-after: 1
    max-spaces-after: 1
  comments:
    require-starting-space: false # 关闭“注释前必须有空格”
    ignore-shebangs: true
    min-spaces-from-content: 1 # 行内注释离正文最少2格
  brackets:
    forbid: false
    min-spaces-inside: 0
    max-spaces-inside: 0 # obj内不应有空格，比如[1, 2, abc]而非[ 1, 2, abc ]
    min-spaces-inside-empty: -1
    max-spaces-inside-empty: -1
  truthy:
    allowed-values: ["true", "false", "on"] # 自定义添加gh-ac的values
    check-keys: true

  #  trailing-spaces: # Use this rule to forbid trailing spaces at the end of lines.
  #    level: warning

  trailing-spaces: disable

  comments-indentation: disable # 关闭“整行注释的缩进”，更舒服
  new-line-at-end-of-file: false # 实际上yml文件不需要留空行
  empty-lines:
    max: 99
    max-end: 9
    max-start: 0

# 忽略文件夹，yamllint需要在配置文件中配置，命令行没有该参数
# invalid config: ignore should contain file patterns
#ignore:
#  - "/node_modules/*"
# 直接从.gitignore中读取ignore文件
ignore-from-file: .gitignore
